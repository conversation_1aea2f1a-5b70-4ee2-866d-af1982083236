#!/usr/bin/env python3
"""
Test PDF upload functionality
"""

import os
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
CORS(app, resources={r"/api/*": {"origins": "*"}})

@app.route('/api/judgement-analyser/upload', methods=['POST'])
def test_pdf_upload():
    """Test PDF upload endpoint"""
    try:
        logger.info("PDF upload endpoint called")
        
        if 'pdf_file' not in request.files:
            return jsonify({'error': 'No PDF file provided'}), 400

        pdf_file = request.files['pdf_file']
        if pdf_file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Read file info
        pdf_bytes = pdf_file.read()
        file_size = len(pdf_bytes)
        
        logger.info(f"Received PDF: {pdf_file.filename}, Size: {file_size} bytes")
        
        # Test basic PDF processing
        try:
            import PyPDF2
            import io
            
            pdf_file_obj = io.BytesIO(pdf_bytes)
            pdf_reader = PyPDF2.PdfReader(pdf_file_obj)
            num_pages = len(pdf_reader.pages)
            
            # Extract first page text
            first_page_text = pdf_reader.pages[0].extract_text()[:200] if num_pages > 0 else ""
            
            return jsonify({
                'status': 'success',
                'message': 'PDF processed successfully',
                'filename': pdf_file.filename,
                'file_size': file_size,
                'num_pages': num_pages,
                'first_page_preview': first_page_text,
                'analysis': f'Successfully processed PDF with {num_pages} pages. This is a test response for PDF analysis.'
            })
            
        except Exception as pdf_error:
            logger.error(f"PDF processing error: {pdf_error}")
            return jsonify({
                'status': 'success',
                'message': 'PDF received but processing failed',
                'filename': pdf_file.filename,
                'file_size': file_size,
                'error': str(pdf_error),
                'analysis': 'PDF upload test successful, but PDF processing encountered an error.'
            })

    except Exception as e:
        logger.error(f"Error in PDF upload: {e}")
        return jsonify({
            'error': 'PDF upload failed',
            'message': f'An error occurred: {str(e)}'
        }), 500

@app.route('/api/health', methods=['GET'])
def health():
    return jsonify({'status': 'healthy', 'message': 'PDF test server running'})

if __name__ == '__main__':
    logger.info("Starting PDF test server...")
    app.run(debug=True, host='127.0.0.1', port=3000, threaded=True)

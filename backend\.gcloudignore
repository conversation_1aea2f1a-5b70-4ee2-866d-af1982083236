# This file specifies files that are *not* uploaded to Google Cloud Platform
# using gcloud. It follows the same syntax as .gitignore, with the addition of
# "#!include" directives (which insert the entries of the given .gitignore-style
# file at that point).

# For more information, run:
#   $ gcloud topic gcloudignore

.gcloudignore
# If you would like to upload your .git directory, .gitignore file or files
# from your .gitignore file, remove the corresponding line below:
.git
.gitignore

# Node.js dependencies:
node_modules/

# Development files
.env.local
.env.development
*.log
logs/
coverage/
.nyc_output/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build artifacts
dist/
build/

# Test files
test/
tests/
__tests__/
*.test.js
*.spec.js

# Documentation
docs/
README.md
*.md

# Other
.eslintrc.*
.prettierrc.*
jest.config.*
babel.config.*

# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Multi-step signup process with role selection
- Authentication context and state management
- Toast notifications system
- Protected route components
- Password strength indicator
- File upload functionality for lawyer licenses
- Form validation with Yup schemas
- Responsive design for mobile devices

### Changed
- Updated signup flow to be more user-friendly
- Improved form validation and error handling
- Enhanced UI animations and transitions

### Fixed
- Mobile navigation issues
- Form validation edge cases
- Password visibility toggle functionality

## [0.2.0] - 2024-01-15

### Added
- Landing page with hero section
- Navigation component with responsive design
- Login page with modern UI
- Basic routing setup
- Tailwind CSS configuration
- Framer Motion animations
- Dark theme implementation

### Changed
- Improved overall design consistency
- Updated color palette for better accessibility
- Enhanced typography and spacing

### Fixed
- Navigation menu on mobile devices
- Button hover states and animations

## [0.1.0] - 2024-01-01

### Added
- Initial project setup
- React + Vite configuration
- Basic project structure
- ESLint and Prettier configuration
- Git repository initialization

### Dependencies
- React 18.2.0
- Vite 5.0.0
- Tailwind CSS 3.4.0
- Framer Motion 10.16.0
- React Router DOM 6.20.0
- React Hook Form 7.48.0
- Yup 1.4.0
- Axios 1.6.0
- React Hot Toast 2.4.0

## [0.0.1] - 2023-12-15

### Added
- Project conception and planning
- Technology stack selection
- Initial design mockups
- Repository creation

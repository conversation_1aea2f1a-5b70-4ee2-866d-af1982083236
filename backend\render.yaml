services:
  - type: web
    name: chainverdict-backend
    env: node
    plan: free
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: CLIENT_URL
        fromService:
          type: web
          name: chainverdict-frontend
          property: url
      - key: FRONTEND_URL
        fromService:
          type: web
          name: chainverdict-frontend
          property: url
      - key: MONGODB_URI
        sync: false
      - key: JWT_SECRET
        sync: false
      - key: JWT_REFRESH_SECRET
        sync: false
      - key: EMAIL_USER
        sync: false
      - key: EMAIL_PASS
        sync: false
      - key: PINATA_API_KEY
        sync: false
      - key: PINATA_SECRET_KEY
        sync: false
      - key: PINATA_JWT
        sync: false

runtime: python313
service: ai-backend

# Environment variables
env_variables:
  GOOGLE_APPLICATION_CREDENTIALS: fluent-music-374010-625191cefb00.json
  FLASK_ENV: production
  PYTHONPATH: /srv

# Instance configuration
instance_class: F2
automatic_scaling:
  min_instances: 1
  max_instances: 10
  target_cpu_utilization: 0.6

# Request timeout (max 60 minutes for App Engine)
entrypoint: gunicorn -b :$PORT app:app --timeout 3600 --workers 2 --threads 4

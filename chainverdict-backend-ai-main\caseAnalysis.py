"""
CaseAnalysis: Legal Case Analysis Framework

An agentic AI framework for comprehensive legal analysis and structured legal arguments,
specializing in Indian law with focus on the Bharatiya Nyaya Sanhita (BNS),
Bharatiya Nagarik <PERSON>aks<PERSON> (BNSS), and Bharatiya Sakshya <PERSON>m (BSA).

This framework provides detailed legal analysis with structured arguments from both sides,
including legal claims, evidence, landmark judgments, and potential outcomes.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Union
import asyncio
import re

# LangChain imports
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.runnables import Runnable, RunnableConfig
from langchain_core.output_parsers import <PERSON>r<PERSON>utputParser, JsonOutputParser
from langchain_core.tools import BaseTool, StructuredTool, Tool

# LLM providers
# from langchain_google_genai import ChatGoogleGenerativeAI  # Commented out
from langchain_openai import ChatOpenAI
from langchain_google_vertexai import ChatVertexAI
from vertexai import init

# Search tools

from langchain_tavily import TavilySearch
from langchain_community.tools import DuckDuckGoSearchResults, WikipediaQueryRun
from langchain_community.utilities import WikipediaAPIWrapper
from langchain_google_community import GoogleSearchAPIWrapper

# Agent frameworks
from langgraph.graph import END, StateGraph
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver

# Configure logging - only show errors
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("CaseAnalysis")

# Get API keys from environment variables and strip quotes if present
GEMINI_API_KEY = "AIzaSyDgF-xkiZhuyKrlJwhSWvClsAAvMSBkctk"
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "").strip('"')
TAVILY_API_KEY = os.getenv("TAVILY_API_KEY", "").strip('"')
GOOGLE_API_KEY = "AIzaSyCc6Bd55VXCZjUSF-Z8EvBIk4v5CgAOD5U"
GOOGLE_CSE_ID = "60a50852da4754279"

# Initialize Vertex AI
init(
    project="fluent-music-374010",
    location="us-central1",
    credentials=r"fluent-music-374010-625191cefb00.json"
)

# Debug: Print API key status (first few characters only for security)
print(f"🔑 Case Analysis API Keys Status:")
print(f"   TAVILY_API_KEY: {'✅ Found' if TAVILY_API_KEY else '❌ Missing'} ({TAVILY_API_KEY[:8]}... if found)")
print(f"   GOOGLE_API_KEY: {'✅ Found' if GOOGLE_API_KEY else '❌ Missing'} ({GOOGLE_API_KEY[:8]}... if found)")
print(f"   GOOGLE_CSE_ID: {'✅ Found' if GOOGLE_CSE_ID else '❌ Missing'} ({GOOGLE_CSE_ID[:8]}... if found)")

# Set environment variables explicitly for any libraries that still look for them
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY or ""
os.environ["GOOGLE_CSE_ID"] = GOOGLE_CSE_ID or ""
os.environ["TAVILY_API_KEY"] = TAVILY_API_KEY or ""


class LegalQuery:
    """Class for legal queries submitted to the system."""
    
    def __init__(self, query_text, language="english", query_type="general", jurisdiction="india", 
                 evidence=None):
        """
        Initialize a legal query.
        
        Args:
            query_text: The legal query or case description
            language: Language of the query (english, hindi, etc.)
            query_type: Type of legal query (criminal, civil, property, etc.)
            jurisdiction: Legal jurisdiction (india, international, etc.)
            evidence: Optional list of evidence items relevant to the case
        """
        self.query_text = query_text
        self.language = language
        self.query_type = query_type
        self.jurisdiction = jurisdiction
        self.evidence = evidence or []


class LegalReference:
    """Class for legal references and citations."""
    
    def __init__(self, code, section, description, punishment=None, url=None):
        """
        Initialize a legal reference.
        
        Args:
            code: Legal code (BNS, BNSS, BSA, IPC, CrPC, etc.)
            section: Section number or identifier
            description: Brief description of the section
            punishment: Associated punishment if applicable
            url: URL to official documentation
        """
        self.code = code
        self.section = section
        self.description = description
        self.punishment = punishment
        self.url = url


class LegalResponse:
    """Class for structured legal responses."""
    
    def __init__(self, summary, applicable_sections=None, procedural_advice=None, 
                 evidence_requirements=None, next_steps=None, references=None,
                 court_process=None, court_fees=None, timeline=None, 
                 user_guidance=None):
        """
        Initialize a legal response.
        
        Args:
            summary: Brief summary of the legal analysis
            applicable_sections: List of applicable legal sections
            procedural_advice: Advice on legal procedures
            evidence_requirements: Required evidence
            next_steps: Recommended next steps
            references: Reference materials and citations
            court_process: Detailed court process to follow
            court_fees: Estimated court fees and costs
            timeline: Average timeline for resolution
            user_guidance: Important information users should know
        """
        self.summary = summary
        self.applicable_sections = applicable_sections or []
        self.procedural_advice = procedural_advice
        self.evidence_requirements = evidence_requirements
        self.next_steps = next_steps or []
        self.references = references or []
        self.court_process = court_process or {}
        self.court_fees = court_fees or {}
        self.timeline = timeline or {}
        self.user_guidance = user_guidance or []


class LegalVerifier:
    """Verifies legal references with supporting evidence from search engines."""
    
    def __init__(self, search_tool=None):
        """
        Initialize the legal verifier.
        
        Args:
            search_tool: Search tool to use for finding supporting evidence
        """
        self.search_tool = search_tool
        if not self.search_tool:
            try:
                # Try to create Google search if not provided
                self.search_tool = GoogleSearchAPIWrapper(
                    google_api_key=GOOGLE_API_KEY, 
                    google_cse_id=GOOGLE_CSE_ID
                )
            except Exception as e:
                logger.warning(f"Could not initialize Google search for verification: {e}")
                self.search_tool = None
    
    def extract_legal_references(self, text):
        """
        Extract legal references from text.
        
        Args:
            text: Text to extract references from
            
        Returns:
            List of extracted legal references
        """
        # Pattern for BNS, BNSS, BSA sections (e.g., BNS 302, Section 302 BNS)
        patterns = [
            r'BNS\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'BNSS\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'BSA\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:the\s+)?(?:BNS|BNSS|BSA)',
            r'[Ss]\.\s*(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:the\s+)?(?:BNS|BNSS|BSA)'
        ]
        
        references = []
        for pattern in patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                # Extract the full match and the section number
                full_match = match.group(0)
                section_num = match.group(1)
                
                # Determine which code (BNS, BNSS, BSA)
                code = "Unknown"
                if "BNS" in full_match:
                    code = "BNS"
                elif "BNSS" in full_match:
                    code = "BNSS"
                elif "BSA" in full_match:
                    code = "BSA"
                
                # Add to references if not already present
                ref = {"code": code, "section": section_num, "full_match": full_match}
                if ref not in references:
                    references.append(ref)
        
        return references
    
    def find_supporting_evidence(self, reference):
        """
        Find supporting evidence for a legal reference.
        
        Args:
            reference: Legal reference to find evidence for
            
        Returns:
            List of supporting evidence
        """
        if not self.search_tool:
            return [{"title": "Search tool unavailable", "link": ""}]
        
        try:
            # Construct search query
            code = reference["code"]
            section = reference["section"]
            query = f"{code} Section {section} Bharatiya legal code reference"
            
            # Perform search
            search_results = self.search_tool.run(query)
            
            # Parse results
            evidence = []
            if isinstance(search_results, str):
                # Extract URLs from text results
                urls = re.findall(r'https?://[^\s]+', search_results)
                titles = re.findall(r'(?:^|\n)([^.\n]+)(?=\.)', search_results)
                
                for i, url in enumerate(urls[:3]):  # Limit to top 3
                    title = titles[i] if i < len(titles) else f"Reference for {code} Section {section}"
                    evidence.append({"title": title, "link": url})
            else:
                # Handle structured results
                for item in search_results[:3]:  # Limit to top 3
                    evidence.append({"title": item.get("title", ""), "link": item.get("link", "")})
            
            return evidence
        except Exception as e:
            logger.error(f"Error finding supporting evidence: {e}")
            return [{"title": f"Error searching for {code} Section {section}", "link": ""}]

    def verify_analysis(self, analysis_text):
        """
        Verify legal analysis with supporting evidence.
        
        Args:
            analysis_text: Legal analysis text to verify
            
        Returns:
            Original text with supporting evidence
        """
        # Extract references
        references = self.extract_legal_references(analysis_text)
        
        if not references:
            return analysis_text
        
        # Find supporting evidence for each reference
        verification_text = "\n\n## Supporting Evidence\n"
        for ref in references:
            code = ref["code"]
            section = ref["section"]
            
            evidence = self.find_supporting_evidence(ref)
            for item in evidence:
                verification_text += f"- {code} {section}: [{item['title']}]({item['link']})\n"
        
        # Combine original analysis with verification
        return analysis_text + verification_text


class LLMProvider:
    """Interface for different LLM providers."""
    
    @staticmethod
    def get_llm(provider: str = "gemini", model: Optional[str] = None, temperature: float = 0) -> Runnable:
        """Factory method to get an LLM based on provider."""
        try:
            logger.info(f"Initializing {provider} LLM model")
            
            if provider == "gemini":
                model_name = model or "gemini-2.5-flash"
                logger.info(f"Using Vertex AI Gemini model: {model_name}")
                return ChatVertexAI(model=model_name)
            
            elif provider == "openai":
                model_name = model or "gpt-4o-mini"
                logger.info(f"Using OpenAI model: {model_name}")
                return ChatOpenAI(
                    model=model_name, 
                    api_key=OPENAI_API_KEY, 
                    temperature=temperature,
                    request_timeout=30  # 30 second timeout
                )
            
            else:
                logger.warning(f"Unknown provider {provider}, defaulting to Vertex AI Gemini")
                return ChatVertexAI(model="gemini-2.5-flash")
                
        except Exception as e:
            error_msg = f"Error initializing LLM ({provider}): {str(e)}"
            logger.error(error_msg)
            logger.exception("Detailed exception information:")
            print(f"ERROR: {error_msg}")
            
            # Fallback to a default LLM
            try:
                logger.info("Attempting fallback to Vertex AI Gemini")
                return ChatVertexAI(model="gemini-2.5-flash")
            except Exception as fallback_error:
                error_msg = f"Fallback LLM initialization also failed: {str(fallback_error)}"
                logger.error(error_msg)
                raise ValueError(error_msg)


class EnhancedCaseAnalysisSearchFactory:
    """🔥 PREMIUM Enhanced Factory for creating advanced case analysis search tools."""

    @staticmethod
    def create_enhanced_tavily_search(max_results: int = 10) -> BaseTool:
        """Create an enhanced Tavily search tool for case analysis."""
        print("🟡 Initializing Enhanced Tavily Search for Case Analysis...")
        if not TAVILY_API_KEY:
            print("   ❌ Tavily API key not found")
            logger.warning("Tavily API key not found")
            return None

        try:
            # Enhanced Tavily search with case analysis focus
            tavily_tool = TavilySearch(
                max_results=max_results,
                api_key=TAVILY_API_KEY,
                search_depth="advanced",
                include_answer=True,
                include_raw_content=True,
                include_images=False,
                include_domains=[
                    "indiankanoon.org", "sci.gov.in", "delhihighcourt.nic.in",
                    "bombayhighcourt.nic.in", "mhc.tn.gov.in", "advocatekhoj.com",
                    "lawyersclubindia.com", "manupatra.com", "scconline.com"
                ]
            )
            print("   ✅ Enhanced Tavily search initialized for case analysis")
            return tavily_tool
        except Exception as e:
            print(f"   ❌ Tavily initialization failed: {e}")
            logger.error(f"Failed to initialize Tavily: {e}")
            return None

    @staticmethod
    def create_enhanced_duckduckgo_search(max_results: int = 10) -> BaseTool:
        """Create an enhanced DuckDuckGo search tool for case analysis."""
        print("🦆 Initializing Enhanced DuckDuckGo Search for Case Analysis...")

        try:
            # Enhanced DuckDuckGo search for case analysis
            from langchain_community.tools import DuckDuckGoSearchResults

            ddg_search = DuckDuckGoSearchResults(
                max_results=max_results,
                region="in-en",  # India region for better legal results
                safesearch="moderate"
            )

            ddg_tool = Tool(
                name="enhanced_case_duckduckgo_search",
                description="🔥 BACKUP: Enhanced DuckDuckGo search for case law research and precedent analysis.",
                func=ddg_search.run,
            )
            print("   ✅ Enhanced DuckDuckGo search initialized for case analysis")
            return ddg_tool
        except Exception as e:
            print(f"   ❌ DuckDuckGo initialization failed: {e}")
            logger.error(f"Failed to initialize DuckDuckGo search: {e}")
            return None

    @staticmethod
    def create_enhanced_wikipedia_search() -> BaseTool:
        """Create an enhanced Wikipedia search tool for case analysis."""
        print("📚 Initializing Enhanced Wikipedia Search for Case Analysis...")

        try:
            # Enhanced Wikipedia search for encyclopedic legal content
            wikipedia_wrapper = WikipediaAPIWrapper(
                top_k_results=5,
                doc_content_chars_max=2000,
                load_all_available_meta=True
            )

            wikipedia_search = WikipediaQueryRun(api_wrapper=wikipedia_wrapper)

            wikipedia_tool = Tool(
                name="enhanced_case_wikipedia_search",
                description="📚 ENCYCLOPEDIC: Enhanced Wikipedia search for legal concepts, case law background, and historical legal context.",
                func=wikipedia_search.run,
            )
            print("   ✅ Enhanced Wikipedia search initialized for case analysis")
            return wikipedia_tool
        except Exception as e:
            print(f"   ❌ Wikipedia initialization failed: {e}")
            logger.error(f"Failed to initialize Wikipedia search: {e}")
            return None

    @staticmethod
    def create_enhanced_google_search(max_results: int = 10) -> BaseTool:
        """Create an enhanced Google search tool for case analysis."""
        print("🌐 Initializing Enhanced Google Search for Case Analysis...")

        try:
            # Enhanced Google search for comprehensive legal research
            google_wrapper = GoogleSearchAPIWrapper(
                google_api_key=GOOGLE_API_KEY,
                google_cse_id=GOOGLE_CSE_ID,
                k=max_results
            )

            google_tool = Tool(
                name="enhanced_case_google_search",
                description="🌐 COMPREHENSIVE: Enhanced Google search for comprehensive case law research and precedent analysis.",
                func=google_wrapper.run,
            )
            print("   ✅ Enhanced Google search initialized for case analysis")
            return google_tool
        except Exception as e:
            print(f"   ❌ Google search initialization failed: {e}")
            logger.error(f"Failed to initialize Google search: {e}")
            return None

    @staticmethod
    def get_premium_case_analysis_tools() -> List[BaseTool]:
        """Get all available premium search tools for case analysis."""
        print("🔧 INITIALIZING PREMIUM CASE ANALYSIS SEARCH TOOLS...")
        print("="*60)
        print("🎯 Total Search Engines: 4 (Google + Tavily + DuckDuckGo + Wikipedia)")

        tools = []

        # Enhanced Tavily search
        tavily_tool = EnhancedCaseAnalysisSearchFactory.create_enhanced_tavily_search(10)
        if tavily_tool:
            tools.append(tavily_tool)

        # Enhanced DuckDuckGo search as backup
        ddg_tool = EnhancedCaseAnalysisSearchFactory.create_enhanced_duckduckgo_search(10)
        if ddg_tool:
            tools.append(ddg_tool)

        # Enhanced Wikipedia search for encyclopedic content
        wikipedia_tool = EnhancedCaseAnalysisSearchFactory.create_enhanced_wikipedia_search()
        if wikipedia_tool:
            tools.append(wikipedia_tool)

        # Enhanced Google search for comprehensive research
        google_tool = EnhancedCaseAnalysisSearchFactory.create_enhanced_google_search(10)
        if google_tool:
            tools.append(google_tool)

        print(f"\n📊 PREMIUM CASE ANALYSIS TOOLS INITIALIZATION COMPLETE:")
        print(f"   🎯 Successfully initialized: {len(tools)}/4 premium search engines")
        print(f"   🔍 Active premium tools: {[tool.name for tool in tools]}")
        print(f"   ⚖️ Case law research optimized with precedent analysis")

        if len(tools) == 0:
            print("   ⚠️ WARNING: No search engines available! Case analysis will be limited.")
        else:
            print("   ✅ PREMIUM CASE ANALYSIS SEARCH ENGINES READY!")

        print("="*60)
        return tools


class CaseAnalysisAgent:
    """Main agent for legal case analysis with structured debate format."""
    
    def __init__(self):
        """Initialize the case analysis agent."""
        self.memory = MemorySaver()
        self.tools = self._initialize_tools()
        self.llm = LLMProvider.get_llm("gemini", "gemini-2.5-pro")
        self.agent_executor = self._initialize_agent()
        self.verifier = LegalVerifier()
        
    def _initialize_tools(self) -> List[BaseTool]:
        """Initialize and return the premium search and analysis tools."""
        # Get premium case analysis search tools (no DuckDuckGo for stability)
        tools = EnhancedCaseAnalysisSearchFactory.get_premium_case_analysis_tools()
        return tools
    
    def _initialize_agent(self) -> Runnable:
        """Initialize and return the agent executor."""
        try:
            logger.info("Creating ReAct agent with tools")
            # Create a ReAct agent
            return create_react_agent(
                self.llm,
                self.tools,
                checkpointer=self.memory
            )
        except Exception as e:
            logger.error(f"Error creating ReAct agent: {e}")
            logger.info("Creating simplified agent without tools")
            
            # Fallback: Create a simpler agent without tools if the ReAct agent fails
            # This just wraps the LLM to maintain API compatibility
            def simple_agent_invoke(inputs, config=None):
                messages = inputs.get("messages", [])
                response = self.llm.invoke(messages)
                return {"messages": [response]}
            
            simple_agent = lambda: None  # Create a dummy object
            simple_agent.invoke = simple_agent_invoke
            simple_agent.stream = lambda inputs, config=None: [simple_agent_invoke(inputs, config)]
            
            return simple_agent

    def _construct_advice_prompt(self, query: LegalQuery) -> str:
        """
        Construct a comprehensive legal advice prompt covering all perspectives, pros, cons, and outcomes.
        """
        prompt = f"""You are a senior legal advisor. Analyze the following legal case from every possible perspective (plaintiff, defendant, third parties, society, etc.).

IMPORTANT: Always respond in ENGLISH regardless of the language of the case description.

CASE DETAILS:
{query.query_text}
"""
        if query.evidence and len(query.evidence) > 0:
            prompt += "\nEVIDENCE PROVIDED:\n"
            for i, evidence_item in enumerate(query.evidence, 1):
                prompt += f"{i}. {evidence_item}\n"
            prompt += "\nIncorporate this evidence in your analysis.\n"
        prompt += """
Your advice MUST include:
- All possible legal outcomes and their likelihood
- Pros and cons for each party (plaintiff, defendant, others)
- Who stands to gain or lose and why
- What is good for each party and what is risky
- All relevant laws, sections, and procedures
- Landmark and recent cases (with citations and Google search references)
- Practical, actionable advice for each party
- Any alternative dispute resolution options
- All advice must be supported by references and Google search results for each major point
- Do NOT generate any debate or round-wise arguments yet

RESPOND IN ENGLISH ONLY.

Format:
1. Executive Summary
2. Detailed Analysis (by party/perspective)
3. Pros and Cons Table
4. All Possible Outcomes
5. Landmark/Relevant Cases (with Google search links)
6. Practical Guidance for Each Party
7. References (with Google search links)
"""
        return prompt

    def analyze_case(self, query: Union[str, LegalQuery], evidence: Optional[List[str]] = None, config: Optional[Dict[str, Any]] = None, include_debate: bool = False) -> str:
        """
        Analyze a legal case and provide comprehensive advice first, then optionally a structured debate.
        
        Args:
            query: A string description of the case or a LegalQuery object
            evidence: Optional list of evidence items to consider in the analysis
            config: Optional configuration for the agent execution
            include_debate: Whether to include the debate after the advice (default: False)
            
        Returns:
            The final verified analysis as a string
        """
        if config is None:
            config = {"configurable": {"thread_id": "case-analysis-001"}}
        if isinstance(query, str):
            query = LegalQuery(query_text=query, evidence=evidence)
        elif evidence and isinstance(query, LegalQuery):
            query.evidence = evidence

        # Step 1: Generate comprehensive advice
        advice_prompt = self._construct_advice_prompt(query)
        advice_system_message = "You are a senior legal advisor. Provide exhaustive, practical, and referenced legal advice as per the prompt. Always use Google search for references and case law. RESPOND IN ENGLISH ONLY regardless of the input language."
        try:
            advice_response = self.llm.invoke([
                SystemMessage(content=advice_system_message),
                HumanMessage(content=advice_prompt)
            ])
            advice_content = advice_response.content if hasattr(advice_response, 'content') else str(advice_response)
            advice_verified = self.verifier.verify_analysis(advice_content)
        except Exception as e:
            advice_verified = f"Advice generation failed: {e}"

        # Step 2: Add a note about debate availability
        advice_verified += "\n\n---\n\n**Note:** If you want to see a detailed round-wise debate between advocates, please set `include_debate=True` when calling this function."

        # Step 3: Generate debate if requested
        if include_debate:
            human_prompt = self._construct_debate_prompt(query)
            system_message = "You are a highly experienced Chief Justice of the Supreme Court of India documenting a legal debate between senior advocates. Follow the previous requirements. Always include references and Google search for every legal point. RESPOND IN ENGLISH ONLY regardless of the input language."
            try:
                response = None
                try:
                    response = self.agent_executor.invoke(
                        {"messages": [
                            SystemMessage(content=system_message),
                            HumanMessage(content=human_prompt)
                        ]},
                        config
                    )
                except Exception as agent_error:
                    llm_response = self.llm.invoke([
                        SystemMessage(content=system_message),
                        HumanMessage(content=human_prompt)
                    ])
                    response = {"messages": [llm_response]}
                content = None
                if isinstance(response, dict) and "agent" in response and "messages" in response["agent"]:
                    messages = response["agent"]["messages"]
                    if messages and len(messages) > 0:
                        message = messages[-1]
                        if hasattr(message, 'content'):
                            content = message.content
                if content is None and isinstance(response, dict) and "messages" in response:
                    messages = response["messages"]
                    if messages and len(messages) > 0:
                        message = messages[-1]
                        if hasattr(message, 'content'):
                            content = message.content
                if content is None and hasattr(response, 'content'):
                    content = response.content
                if content:
                    verified_content = self.verifier.verify_analysis(content)
                    return advice_verified + "\n\n---\n\n## LEGAL DEBATE\n\n" + verified_content
                else:
                    return advice_verified + "\n\n---\n\nDebate generation failed."
            except Exception as e:
                return advice_verified + f"\n\n---\n\nDebate generation failed: {e}"
        
        return advice_verified
    
    def stream_analysis(self, query: Union[str, LegalQuery], evidence: Optional[List[str]] = None, config: Optional[Dict[str, Any]] = None):
        """
        Stream the analysis results as they are generated.
        
        Args:
            query: A string description of the case or a LegalQuery object
            evidence: Optional list of evidence items to consider in the analysis
            config: Optional configuration for the agent execution
            
        Returns:
            A generator yielding chunks of the agent's response with the final chunk containing verified content
        """
        if config is None:
            config = {"configurable": {"thread_id": "case-analysis-001"}}
            
        # Convert string query to LegalQuery if needed
        if isinstance(query, str):
            query = LegalQuery(query_text=query, evidence=evidence)
        elif evidence and isinstance(query, LegalQuery):
            query.evidence = evidence
            
        # Construct the prompt
        human_prompt = self._construct_debate_prompt(query)
        
        # Stream chunks through the agent executor
        logger.info("Starting streamed legal case analysis")
        chunks = []
        
        try:
            # First try to stream with the agent executor
            try:
                for chunk in self.agent_executor.stream(
                    {"messages": [
                        SystemMessage(content="You are a legal expert specialized in Indian law."),
                        HumanMessage(content=human_prompt)
                    ]},
                    config
                ):
                    chunks.append(chunk)
                    yield chunk  # Yield each chunk as it comes in
                    
                logger.info(f"Streaming completed with {len(chunks)} chunks")
                
            except Exception as stream_error:
                logger.warning(f"Agent streaming failed: {str(stream_error)}")
                logger.info("Falling back to direct LLM call")
                
                # Fallback to direct LLM call (won't be streamed)
                try:
                    llm_response = self.llm.invoke([
                        SystemMessage(content="You are a legal expert specialized in Indian law."),
                        HumanMessage(content=human_prompt)
                    ])
                    
                    # Create a chunk mimicking the agent executor format
                    fallback_chunk = {"messages": [llm_response]}
                    chunks.append(fallback_chunk)
                    yield fallback_chunk
                    
                except Exception as llm_error:
                    logger.error(f"Direct LLM call also failed: {str(llm_error)}")
                    error_chunk = {"error": f"Both streaming and direct LLM failed: {str(llm_error)}"}
                    yield error_chunk
                    return
            
            # If we have at least one chunk, verify the last one before returning it
            if chunks:
                last_chunk = chunks[-1]
                content = None
                
                # Try to extract content from the last chunk
                if isinstance(last_chunk, dict):
                    # Try agent.messages structure
                    if "agent" in last_chunk and "messages" in last_chunk["agent"]:
                        messages = last_chunk["agent"]["messages"]
                        if messages and len(messages) > 0:
                            message = messages[-1]
                            if hasattr(message, 'content'):
                                content = message.content
                    
                    # Try direct messages structure
                    elif "messages" in last_chunk:
                        messages = last_chunk["messages"]
                        if messages and len(messages) > 0:
                            message = messages[-1]
                            if hasattr(message, 'content'):
                                content = message.content
                
                # If we found content, verify it and yield a final verified chunk
                if content:
                    logger.info("Verifying legal references in final streamed response")
                    verified_content = self.verifier.verify_analysis(content)
                    
                    # Create a new verified chunk (follow the same format as the last chunk)
                    verified_chunk = dict(last_chunk)  # Copy the structure
                    
                    # Update the content in the appropriate place
                    if "agent" in verified_chunk and "messages" in verified_chunk["agent"]:
                        verified_chunk["agent"]["messages"][-1] = AIMessage(content=verified_content)
                    elif "messages" in verified_chunk:
                        verified_chunk["messages"][-1] = AIMessage(content=verified_content)
                    
                    # Yield the final verified chunk
                    yield verified_chunk
                    logger.info("Yielded final verified chunk")
                
        except Exception as e:
            error_msg = f"Error in streaming analysis: {str(e)}"
            logger.error(error_msg)
            logger.exception("Exception details:")
            yield {"error": error_msg}
            
    def _construct_debate_prompt(self, query: LegalQuery) -> str:
        """
        Construct a legal debate analysis prompt based on the query.
        
        Args:
            query: The LegalQuery object
            
        Returns:
            A formatted prompt string
        """
        # Base prompt with case details
        prompt = f"""Generate a structured legal debate between two senior advocates arguing a property dispute case with the following details:

IMPORTANT: Always respond in ENGLISH regardless of the language of the case description.

CASE FACTS:
{query.query_text}
"""

        # Add evidence section if provided
        if query.evidence and len(query.evidence) > 0:
            prompt += "\nEVIDENCE PROVIDED:\n"
            for i, evidence_item in enumerate(query.evidence, 1):
                prompt += f"{i}. {evidence_item}\n"
            prompt += "\nPlease incorporate analysis of this evidence in the advocates' arguments.\n"

        # Complete the prompt with debate format instructions
        prompt += """
The debate must be formatted as a formal court proceeding with THREE ROUNDS of arguments, each round having:
1. Plaintiff's Advocate's Arguments (Round X)
2. Defendant's Advocate's Response (Round X)

AFTER the debate, include a PRACTICAL GUIDANCE section with the following:
1. Court Process: Detailed step-by-step process for the user to follow, including document filing requirements
2. Court Fees: Breakdown of all associated costs and fees for each stage of litigation 
3. Timeline: Average time required for each stage and complete resolution
4. Important Information: Critical knowledge users should have about the process, risks, and alternatives

RESPOND IN ENGLISH ONLY.

DO NOT repeat these instructions in your response. Begin directly with "ROUND 1: PLAINTIFF'S ADVOCATE'S ARGUMENTS" followed by the substantive legal arguments.
"""
        return prompt


# Main execution function
def analyze_legal_case(case_description, evidence=None, include_debate=False):
    """
    Analyze a legal case and provide comprehensive advice, optionally with structured debate arguments.
    
    Args:
        case_description: A string description of the legal case
        evidence: Optional list of evidence items relevant to the case
        include_debate: Whether to include the debate after the advice (default: False)
        
    Returns:
        The analysis results
    """
    agent = CaseAnalysisAgent()
    
    print("Starting case analysis...")
    
    try:
        # Analyze the case with the provided evidence
        result = agent.analyze_case(case_description, evidence=evidence, include_debate=include_debate)
        return result
    except Exception as e:
        import traceback
        print(f"Error during analysis: {e}")
        print(traceback.format_exc())
        return f"Analysis failed: {e}"


# Example usage
if __name__ == "__main__":
    case_description = """
    विवरण:
    राम और श्याम के बीच एक संपत्ति विवाद चल रहा है। उनके पिता ने मरने से पहले कोई वसीयत नहीं छोड़ी थी, जिसके कारण दोनों भाइयों में संपत्ति के बंटवारे को लेकर विवाद हो गया है। राम का दावा है कि वह संपत्ति पर पूरा हक रखता है क्योंकि उसने वर्षों तक उसकी देखभाल की है, जबकि श्याम का कहना है कि संपत्ति पर दोनों का समान अधिकार है।

    प्रमुख मुद्दे:
    संपत्ति का कानूनी स्वामित्व
    उत्तराधिकार अधिनियम के तहत अधिकार
    परिवारिक बंटवारे के नियम
    """
    
    # Example of providing evidence (optional)
    evidence = [
        "Property maintenance receipts showing Ram paid for upkeep for 10 years",
        "Family photographs showing Shyam visiting the property during festivals",
        "Bank statements showing Ram paid property taxes",
        "Letter from father stating his wish for brothers to share the property equally",
        "Village panchayat record showing both brothers listed as potential heirs"
    ]
    
    # Initialize the case analysis agent
    print("Initializing case analysis agent...")
    agent = CaseAnalysisAgent()
    
    # First, get comprehensive advice only
    print("Starting comprehensive legal advice analysis...")
    try:
        advice_result = agent.analyze_case(case_description, evidence=evidence, include_debate=False)
        print("\n\nCOMPREHENSIVE LEGAL ADVICE:\n")
        print(advice_result)
        
        # Optionally, also show the debate
        print("\n" + "="*80)
        print("Now generating advice WITH debate...")
        print("="*80)
        
        full_result = agent.analyze_case(case_description, evidence=evidence, include_debate=True)
        print("\n\nFULL ANALYSIS WITH DEBATE:\n")
        print(full_result)
        
    except Exception as e:
        import traceback
        print(f"Error during analysis: {e}")
        print(traceback.format_exc())
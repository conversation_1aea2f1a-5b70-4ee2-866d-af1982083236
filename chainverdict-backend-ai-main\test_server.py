#!/usr/bin/env python3
"""
Simple test server to verify <PERSON><PERSON><PERSON> and <PERSON><PERSON> are working
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
CORS(app, resources={r"/api/*": {"origins": "*"}})

@app.route('/api/test', methods=['GET'])
def test():
    logger.info("Test endpoint called")
    return jsonify({
        'status': 'success',
        'message': 'Backend server is working!',
        'timestamp': '2025-07-27'
    })

@app.route('/api/judgement-analyser', methods=['POST'])
def test_judgement():
    logger.info("Judgement analyser test endpoint called")
    data = request.get_json()
    return jsonify({
        'status': 'success',
        'message': 'Judgement analyser endpoint is working!',
        'received_data': data
    })

@app.route('/api/judgement-analyser/upload', methods=['POST'])
def test_judgement_upload():
    logger.info("Judgement analyser upload test endpoint called")
    return jsonify({
        'status': 'success',
        'message': 'Judgement analyser upload endpoint is working!'
    })

if __name__ == '__main__':
    logger.info("Starting test server...")
    app.run(debug=True, host='127.0.0.1', port=3000, threaded=True)

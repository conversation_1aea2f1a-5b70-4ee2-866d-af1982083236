# Server Configuration
PORT=5000
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/cv-platform

# JWT Secrets
JWT_SECRET=your-secret-key-here
JWT_REFRESH_SECRET=your-refresh-secret-key-here

# Google reCAPTCHA
RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key

# Admin Account
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Admin@123456

# Email Configuration
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Alternative SMTP Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Frontend URL for email links
FRONTEND_URL=http://localhost:5173

# Socket.IO Configuration
SOCKET_CORS_ORIGIN=http://localhost:5173

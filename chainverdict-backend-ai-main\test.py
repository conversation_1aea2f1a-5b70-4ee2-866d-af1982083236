# from vertexai import init
# from langchain_google_vertexai import ChatVertexAI

# # Initialize Vertex AI
# init(
#     project="fluent-music-374010",
#     location="us-central1",
#     credentials=r"C:\Users\<USER>\Documents\fluent-music-374010-625191cefb00.json"
# )

# # Use the Gemini model
# llm = ChatVertexAI(model="gemini-2.5-flash").bind_tools([{"google_search": {}}])

# response = llm.invoke([
#     ("system", "You are a helpful ai Legal Assistant which provide real time data."),
#     ("human", "Explain BNS section 101?")
# ])

# print(response.content)

# # To install: pip install tavily-python
# from tavily import TavilyClient
# client = TavilyClient("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")
# response = client.search(
#     query="What are the key differences between GPT-4 and Claude 2?"
# )
# print(response)

from langchain_community.tools import WikipediaQueryRun
from langchain_community.utilities import WikipediaAPIWrapper

wikipedia = WikipediaQueryRun(api_wrapper=WikipediaAPIWrapper())
res = wikipedia.run("bihar vidhansabha election 2025")
print(res)
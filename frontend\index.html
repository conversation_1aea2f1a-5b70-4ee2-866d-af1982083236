<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/icon512_rounded.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ChainVerdict</title>

    <!-- PWA Meta Tags -->
    <meta
      name="description"
      content="Expert legal advice platform connecting citizens with verified lawyers"
    />
    <meta name="theme-color" content="#8936FF" />
    <meta name="background-color" content="#2EC6FE" />

    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="/icon512_rounded.png" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <meta name="apple-mobile-web-app-title" content="ChainVerdict" />

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#8936FF" />
    <meta name="msapplication-TileImage" content="/icon512_rounded.png" />

    <!-- Manifest -->
    <link rel="manifest" href="/manifest.json" />
    <script>
      // Polyfill for simple-peer
      if (typeof global === "undefined") {
        var global = globalThis;
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>

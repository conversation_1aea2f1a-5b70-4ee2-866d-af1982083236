import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authAPI } from '../services/api';

// Initial state
const initialState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  CLEAR_ERROR: 'CLEAR_ERROR',
  UPDATE_PROFILE: 'UPDATE_PROFILE',
};

// Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };
    case AUTH_ACTIONS.SET_USER:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
      };
    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };
    case AUTH_ACTIONS.UPDATE_PROFILE:
      return {
        ...state,
        user: { ...state.user, ...action.payload },
      };
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Token management utilities
const TOKEN_KEY = 'cv_access_token';
const USER_KEY = 'cv_user_data';

const getStoredToken = () => {
  try {
    return localStorage.getItem(TOKEN_KEY);
  } catch (error) {
    console.error('Error getting stored token:', error);
    return null;
  }
};

const setStoredToken = (token) => {
  try {
    if (token) {
      localStorage.setItem(TOKEN_KEY, token);
    } else {
      localStorage.removeItem(TOKEN_KEY);
    }
  } catch (error) {
    console.error('Error storing token:', error);
  }
};

const getStoredUser = () => {
  try {
    const userData = localStorage.getItem(USER_KEY);
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error getting stored user:', error);
    return null;
  }
};

const setStoredUser = (user) => {
  try {
    if (user) {
      localStorage.setItem(USER_KEY, JSON.stringify(user));
    } else {
      localStorage.removeItem(USER_KEY);
    }
  } catch (error) {
    console.error('Error storing user:', error);
  }
};

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const storedToken = getStoredToken();
        const storedUser = getStoredUser();

        if (storedToken && storedUser) {
          // Verify token is still valid by making a request to /auth/me
          // For now, just set the stored data
          dispatch({
            type: AUTH_ACTIONS.LOGIN_SUCCESS,
            payload: {
              token: storedToken,
              user: storedUser,
            },
          });
        } else {
          dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      }
    };

    initializeAuth();
  }, []);

  // Auth actions
  const login = async (token, user) => {
    try {
      dispatch({ type: AUTH_ACTIONS.LOGIN_START });
      
      // Store token and user data
      setStoredToken(token);
      setStoredUser(user);
      
      dispatch({
        type: AUTH_ACTIONS.LOGIN_SUCCESS,
        payload: { token, user },
      });
      
      return { success: true };
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: error.message || 'Login failed',
      });
      return { success: false, error: error.message };
    }
  };

  const logout = async () => {
    try {
      // Clear stored data
      setStoredToken(null);
      setStoredUser(null);
      
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
      
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, error: error.message };
    }
  };

  const updateProfile = (updatedUser) => {
    setStoredUser(updatedUser);
    dispatch({
      type: AUTH_ACTIONS.UPDATE_PROFILE,
      payload: updatedUser,
    });
  };

  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  // Refresh current user data
  const refreshUser = async () => {
    try {
      if (!state.token) {
        return { success: false, error: 'No token available' };
      }

      const response = await authAPI.getCurrentUser();

      if (response.success) {
        const updatedUser = response.data.user;
        setStoredUser(updatedUser);
        dispatch({
          type: AUTH_ACTIONS.UPDATE_PROFILE,
          payload: updatedUser,
        });
        return { success: true, data: updatedUser };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      console.error('Refresh user error:', error);
      return { success: false, error: error.message };
    }
  };

  // Role-based access control helpers
  const hasRole = (role) => {
    return state.user?.role === role;
  };

  const isLawyer = () => hasRole('lawyer');
  const isCitizen = () => hasRole('citizen');
  const isAdmin = () => hasRole('admin');

  // Check if lawyer is verified
  const isVerifiedLawyer = () => {
    return hasRole('lawyer') && state.user?.isVerified === true;
  };

  // Get verification status for lawyers
  const getVerificationStatus = () => {
    if (!hasRole('lawyer')) return null;
    return {
      isVerified: state.user?.isVerified || false,
      verificationStatus: state.user?.lawyerDetails?.verificationStatus || 'pending'
    };
  };

  const canAccess = (allowedRoles) => {
    if (!state.isAuthenticated) return false;
    if (!allowedRoles || allowedRoles.length === 0) return true;

    const userRole = state.user?.role;
    const hasRequiredRole = allowedRoles.includes(userRole);

    // If user doesn't have the required role, deny access
    if (!hasRequiredRole) return false;

    // Special check for lawyers - they must be verified to access lawyer features
    if (userRole === 'lawyer' && allowedRoles.includes('lawyer')) {
      return state.user?.isVerified === true;
    }

    return true;
  };

  // Context value
  const value = {
    // State
    ...state,

    // Actions
    login,
    logout,
    updateProfile,
    clearError,
    refreshUser,

    // Helpers
    hasRole,
    isLawyer,
    isCitizen,
    isAdmin,
    isVerifiedLawyer,
    getVerificationStatus,
    canAccess,

    // Token for API calls
    getToken: () => state.token,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;

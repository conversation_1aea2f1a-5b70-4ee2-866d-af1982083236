/* Active Call Interface Styles */
.active-call-interface {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #1a1a1a;
  z-index: 9998;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.active-call-interface.minimized {
  top: auto;
  bottom: 20px;
  right: 20px;
  left: auto;
  width: 300px;
  height: 200px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

/* Call Header */
.call-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.participant-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.participant-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar {
  color: white;
}

.participant-details h3 {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.call-duration {
  color: #ccc;
  font-size: 14px;
  margin: 2px 0 0 0;
}

.call-header-actions {
  display: flex;
  gap: 8px;
}

.minimize-button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  padding: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.minimize-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Video Container */
.video-container {
  flex: 1;
  position: relative;
  background: #000;
}

.remote-video {
  width: 100%;
  height: 100%;
  position: relative;
}

.remote-video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.local-video {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 150px;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: #333;
}

.local-video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #ccc;
  background: #333;
}

.video-placeholder.small {
  color: #999;
}

.video-placeholder p {
  margin: 10px 0 0 0;
  font-size: 14px;
}

/* Voice Call Display */
.voice-call-display {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.voice-avatar {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 20px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
}

.voice-participant-name {
  color: white;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
  text-align: center;
}

.voice-call-status {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin: 0;
}

/* Call Controls */
.call-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  padding: 30px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
}

.control-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.control-button:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 0.2);
}

.control-button:active {
  transform: scale(0.95);
}

.control-button.active {
  background: rgba(255, 255, 255, 0.3);
}

.control-button.end-call {
  background: linear-gradient(135deg, #ff4757, #ff3838);
}

.control-button.end-call:hover {
  background: linear-gradient(135deg, #ff3838, #ff2f2f);
}

/* Minimized State */
.active-call-interface.minimized .call-header {
  padding: 12px;
}

.active-call-interface.minimized .participant-avatar {
  width: 30px;
  height: 30px;
}

.active-call-interface.minimized .participant-details h3 {
  font-size: 14px;
}

.active-call-interface.minimized .call-duration {
  font-size: 12px;
}

.active-call-interface.minimized .video-container {
  display: none;
}

.active-call-interface.minimized .voice-call-display {
  display: none;
}

.active-call-interface.minimized .call-controls {
  padding: 15px;
  gap: 10px;
}

.active-call-interface.minimized .control-button {
  width: 40px;
  height: 40px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .local-video {
    width: 100px;
    height: 130px;
    top: 10px;
    right: 10px;
  }
  
  .voice-avatar {
    width: 120px;
    height: 120px;
  }
  
  .voice-participant-name {
    font-size: 24px;
  }
  
  .call-controls {
    gap: 15px;
    padding: 20px;
  }
  
  .control-button {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .call-header {
    padding: 15px;
  }
  
  .participant-details h3 {
    font-size: 14px;
  }
  
  .call-duration {
    font-size: 12px;
  }
  
  .local-video {
    width: 80px;
    height: 100px;
  }
  
  .voice-avatar {
    width: 100px;
    height: 100px;
  }
  
  .voice-participant-name {
    font-size: 20px;
  }
  
  .call-controls {
    gap: 12px;
    padding: 15px;
  }
  
  .control-button {
    width: 45px;
    height: 45px;
  }
}

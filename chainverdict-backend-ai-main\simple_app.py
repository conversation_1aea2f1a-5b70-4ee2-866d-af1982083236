#!/usr/bin/env python3
"""
Simplified ChainVerdict AI Backend
Focus on judgement analyser with proper error handling
"""

import os
import logging
import json
from typing import Dict, List, Any, Optional, Union
from flask import Flask, request, jsonify
from flask_cors import CORS
import uuid
import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ChainVerdictAI")

# Create Flask app
app = Flask(__name__)
CORS(app, resources={r"/api/*": {"origins": "*"}})

# Configuration
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size
app.config['UPLOAD_TIMEOUT'] = 300  # 5 minutes timeout for uploads
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # Disable caching for development

# Initialize components with error handling
judgement_analyser = None
bns_advisor = None
case_analysis_agent = None
legal_research = None

def extract_content(obj):
    """Extract content from various AI response formats"""
    if hasattr(obj, 'content'):
        return obj.content
    elif isinstance(obj, dict):
        if 'messages' in obj and obj['messages']:
            # Extract from messages array
            message = obj['messages'][-1]  # Get last message
            if hasattr(message, 'content'):
                return message.content
            return str(message)
        elif 'content' in obj:
            return obj['content']
        elif 'analysis' in obj:
            return obj['analysis']
        else:
            return str(obj)
    elif isinstance(obj, list) and obj:
        # If it's a list, try to extract from first item
        return extract_content(obj[0])
    else:
        return str(obj)

def initialize_components():
    """Initialize AI components with proper error handling"""
    global judgement_analyser, bns_advisor, case_analysis_agent, legal_research
    
    try:
        logger.info("Initializing JudgementAnalyser...")
        from judgementAnalyser import JudgementAnalyser
        judgement_analyser = JudgementAnalyser()
        logger.info("✅ JudgementAnalyser initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize JudgementAnalyser: {e}")
        judgement_analyser = None

    try:
        logger.info("Initializing BNSAdvisor...")
        from bnsAdvisor import BNSAdvisor
        bns_advisor = BNSAdvisor()
        logger.info("✅ BNSAdvisor initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize BNSAdvisor: {e}")
        bns_advisor = None

    try:
        logger.info("Initializing CaseAnalysisAgent...")
        from caseAnalysis import CaseAnalysisAgent
        case_analysis_agent = CaseAnalysisAgent()
        logger.info("✅ CaseAnalysisAgent initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize CaseAnalysisAgent: {e}")
        case_analysis_agent = None

    try:
        logger.info("Initializing LegalResearch...")
        from legalResearch import LegalResearch
        legal_research = LegalResearch()
        logger.info("✅ LegalResearch initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize LegalResearch: {e}")
        legal_research = None

# Health check endpoint
@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.datetime.now().isoformat(),
        'components': {
            'judgement_analyser': judgement_analyser is not None,
            'bns_advisor': bns_advisor is not None,
            'case_analysis_agent': case_analysis_agent is not None,
            'legal_research': legal_research is not None
        }
    })

# Judgement Analyser - Text Analysis
@app.route('/api/judgement-analyser', methods=['POST'])
def analyze_judgement():
    """Analyze judgement text"""
    try:
        if judgement_analyser is None:
            return jsonify({
                'error': 'Judgement Analyser not available',
                'message': 'The judgement analysis service is currently unavailable. Please try again later.'
            }), 503

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        judgement_details = data.get('judgement_details', '')
        crime_details = data.get('crime_details', '')
        evidence_details = data.get('evidence_details', '')
        verify = data.get('verify', True)
        include_detailed_analysis = data.get('include_detailed_analysis', False)

        if not judgement_details:
            return jsonify({'error': 'judgement_details is required'}), 400

        logger.info(f"Analyzing judgement: {judgement_details[:100]}...")
        
        # Call the judgement analyser
        result = judgement_analyser.analyze_judgement(
            judgement_details=judgement_details,
            crime_details=crime_details,
            evidence_details=evidence_details,
            verify=verify,
            include_detailed_analysis=include_detailed_analysis
        )

        # Convert result to JSON-serializable format
        analysis_content = extract_content(result)
        response_data = {
            'status': 'success',
            'analysis': analysis_content,
            'timestamp': datetime.datetime.now().isoformat()
        }

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error in judgement analysis: {e}")
        return jsonify({
            'error': 'Analysis failed',
            'message': f'An error occurred during analysis: {str(e)}'
        }), 500

# Judgement Analyser - PDF Upload
@app.route('/api/judgement-analyser/upload', methods=['POST'])
def analyze_judgement_pdf():
    """Analyze judgement PDF"""
    try:
        if judgement_analyser is None:
            return jsonify({
                'error': 'Judgement Analyser not available',
                'message': 'The judgement analysis service is currently unavailable. Please try again later.'
            }), 503

        if 'pdf_file' not in request.files:
            return jsonify({'error': 'No PDF file provided'}), 400

        pdf_file = request.files['pdf_file']
        if pdf_file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        include_detailed_analysis = request.form.get('include_detailed_analysis', 'false').lower() == 'true'

        logger.info(f"Analyzing PDF: {pdf_file.filename}")
        
        # Read PDF file bytes
        pdf_bytes = pdf_file.read()

        # Call the judgement analyser with PDF
        result = judgement_analyser.analyze_pdf_judgement(
            pdf_bytes=pdf_bytes,
            include_detailed_analysis=include_detailed_analysis
        )

        # Convert result to JSON-serializable format
        analysis_content = extract_content(result)
        response_data = {
            'status': 'success',
            'analysis': analysis_content,
            'timestamp': datetime.datetime.now().isoformat()
        }

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error in PDF judgement analysis: {e}")
        return jsonify({
            'error': 'PDF analysis failed',
            'message': f'An error occurred during PDF analysis: {str(e)}'
        }), 500

# BNS Advisor endpoint
@app.route('/api/bns-advisor', methods=['POST'])
def bns_advisor_endpoint():
    """BNS Advisor analysis"""
    try:
        if bns_advisor is None:
            return jsonify({
                'error': 'BNS Advisor not available',
                'message': 'The BNS Advisor service is currently unavailable. Please try again later.'
            }), 503

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        case_description = data.get('case_description', '')
        verify = data.get('verify', True)

        if not case_description:
            return jsonify({'error': 'case_description is required'}), 400

        logger.info(f"BNS Analysis: {case_description[:100]}...")

        result = bns_advisor.analyze_case(case_description, verify)

        # Convert result to JSON-serializable format
        analysis_content = extract_content(result)
        response_data = {
            'status': 'success',
            'analysis': analysis_content,
            'timestamp': datetime.datetime.now().isoformat()
        }

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error in BNS analysis: {e}")
        return jsonify({
            'error': 'BNS analysis failed',
            'message': f'An error occurred during analysis: {str(e)}'
        }), 500

# Legal Advisor endpoint
@app.route('/api/legal-advisor', methods=['POST'])
def legal_advisor_endpoint():
    """Legal Advisor analysis"""
    try:
        if case_analysis_agent is None:
            return jsonify({
                'error': 'Legal Advisor not available',
                'message': 'The Legal Advisor service is currently unavailable. Please try again later.'
            }), 503

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        query = data.get('query', '')
        include_debate = data.get('include_debate', False)
        chat_id = data.get('chat_id', None)

        if not query:
            return jsonify({'error': 'query is required'}), 400

        logger.info(f"Legal Advisor: {query[:100]}...")

        result = case_analysis_agent.analyze_case(query, include_debate, chat_id)

        # Convert result to JSON-serializable format
        analysis_content = extract_content(result)
        response_data = {
            'status': 'success',
            'analysis': analysis_content,
            'timestamp': datetime.datetime.now().isoformat()
        }

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error in Legal Advisor analysis: {e}")
        return jsonify({
            'error': 'Legal Advisor analysis failed',
            'message': f'An error occurred during analysis: {str(e)}'
        }), 500

if __name__ == '__main__':
    try:
        logger.info("Starting ChainVerdict AI Backend...")
        initialize_components()
        logger.info("All components initialized. Starting server...")
        app.run(debug=True, host='127.0.0.1', port=3000, threaded=True)
    except Exception as e:
        logger.error(f"Failed to start server: {e}")

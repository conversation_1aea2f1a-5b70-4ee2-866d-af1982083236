<svg width="144" height="144" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="256" cy="256" r="256" fill="url(#gradient)"/>
  
  <!-- Legal Scale Icon -->
  <g transform="translate(156, 156)">
    <!-- Scale Base -->
    <rect x="90" y="180" width="20" height="40" fill="white" rx="2"/>
    <rect x="70" y="215" width="60" height="8" fill="white" rx="4"/>
    
    <!-- Scale Beam -->
    <rect x="50" y="95" width="100" height="6" fill="white" rx="3"/>
    
    <!-- Left Scale Pan -->
    <path d="M60 95 L40 125 L80 125 Z" fill="white"/>
    <rect x="35" y="125" width="50" height="4" fill="white" rx="2"/>
    
    <!-- Right Scale Pan -->
    <path d="M140 95 L120 125 L160 125 Z" fill="white"/>
    <rect x="115" y="125" width="50" height="4" fill="white" rx="2"/>
    
    <!-- Center Pivot -->
    <circle cx="100" cy="95" r="8" fill="white"/>
    <circle cx="100" cy="95" r="4" fill="#1e40af"/>
  </g>
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>

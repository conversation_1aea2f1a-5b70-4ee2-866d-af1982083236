{"name": "backend", "version": "1.0.0", "description": "Case Management & Dispute Resolution System Backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node seeds/adminSeed.js", "fix-verification": "node scripts/fixVerificationStatus.js"}, "keywords": ["case-management", "dispute-resolution", "legal-tech"], "author": "", "license": "ISC", "type": "module", "dependencies": {"@heroicons/react": "^2.2.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "form-data": "^4.0.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.0", "morgan": "^1.10.0", "multer": "^2.0.2", "nodemailer": "^6.10.1", "react-hot-toast": "^2.5.2", "socket.io": "^4.6.1", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.0.1"}}
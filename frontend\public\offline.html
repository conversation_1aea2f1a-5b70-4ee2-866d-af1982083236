<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Offline - ChainVerdict</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: linear-gradient(135deg, #8936ff 0%, #2ec6fe 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        text-align: center;
        padding: 20px;
      }

      .offline-container {
        max-width: 500px;
        padding: 40px;
        background: rgba(255, 255, 255, 0.1);
        -webkit-backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      }

      .offline-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 30px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 40px;
      }

      h1 {
        font-size: 2.5rem;
        margin-bottom: 20px;
        background: linear-gradient(45deg, #ffffff, #f0f0f0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      p {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 30px;
        color: rgba(255, 255, 255, 0.9);
      }

      .retry-button {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        padding: 15px 30px;
        border-radius: 10px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        -webkit-backdrop-filter: blur(10px);
        backdrop-filter: blur(10px);
      }

      .retry-button:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
      }

      .features {
        margin-top: 40px;
        text-align: left;
      }

      .feature {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        color: rgba(255, 255, 255, 0.8);
      }

      .feature-icon {
        width: 20px;
        height: 20px;
        margin-right: 15px;
        color: #ffffff;
      }

      @media (max-width: 600px) {
        .offline-container {
          padding: 30px 20px;
        }

        h1 {
          font-size: 2rem;
        }

        .offline-icon {
          width: 60px;
          height: 60px;
          font-size: 30px;
        }
      }
    </style>
  </head>
  <body>
    <div class="offline-container">
      <div class="offline-icon">⚖️</div>

      <h1>You're Offline</h1>

      <p>
        It looks like you've lost your internet connection. Don't worry - you
        can still browse previously loaded content and ChainVerdict will sync
        when you're back online.
      </p>

      <button class="retry-button" onclick="window.location.reload()">
        Try Again
      </button>

      <div class="features">
        <div class="feature">
          <span class="feature-icon">📱</span>
          <span>App works offline with cached content</span>
        </div>
        <div class="feature">
          <span class="feature-icon">🔄</span>
          <span>Automatic sync when connection returns</span>
        </div>
        <div class="feature">
          <span class="feature-icon">💾</span>
          <span>Your data is safely stored locally</span>
        </div>
      </div>
    </div>

    <script>
      // Check for connection and auto-reload when back online
      window.addEventListener("online", () => {
        console.log("Connection restored, reloading...");
        window.location.reload();
      });

      // Show connection status
      if (navigator.onLine) {
        console.log("Online");
      } else {
        console.log("Offline");
      }
    </script>
  </body>
</html>

/* Call Interface Animations */

.audio-wave {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2px;
  margin-top: 1rem;
}

.audio-bar {
  width: 3px;
  background: linear-gradient(to top, #06b6d4, #0891b2);
  border-radius: 2px;
  animation: audioWave 1.5s ease-in-out infinite;
}

.audio-bar:nth-child(1) { animation-delay: 0s; }
.audio-bar:nth-child(2) { animation-delay: 0.1s; }
.audio-bar:nth-child(3) { animation-delay: 0.2s; }
.audio-bar:nth-child(4) { animation-delay: 0.3s; }
.audio-bar:nth-child(5) { animation-delay: 0.4s; }

@keyframes audioWave {
  0%, 100% {
    height: 8px;
    opacity: 0.4;
  }
  50% {
    height: 24px;
    opacity: 1;
  }
}

.call-pulse {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.call-ring {
  position: relative;
}

.call-ring::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid rgba(6, 182, 212, 0.3);
  border-radius: 50%;
  animation: ring 2s ease-in-out infinite;
}

.call-ring::after {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border: 2px solid rgba(6, 182, 212, 0.2);
  border-radius: 50%;
  animation: ring 2s ease-in-out infinite 0.5s;
}

@keyframes ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.video-placeholder {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  position: relative;
  overflow: hidden;
}

.video-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@import "tailwindcss";

/* Custom Animations */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes card-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.1), 0 0 40px rgba(147, 51, 234, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.3), 0 0 60px rgba(147, 51, 234, 0.2);
  }
}

@keyframes card-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.animate-card-glow {
  animation: card-glow 3s ease-in-out infinite;
}

.animate-card-pulse {
  animation: card-pulse 2s ease-in-out infinite;
}

/* Glassmorphism Effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.3);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient Text Effects */
.gradient-text {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-shift 3s ease infinite;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #2563eb, #7c3aed);
}

/* Enhanced Button Styles */
.btn-primary {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.4);
}

/* Card Hover Effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

/* Enhanced Sexy Card Hover Effects */
.card-sexy {
  position: relative;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center;
  overflow: hidden;
}

.card-sexy::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
  z-index: 1;
}

.card-sexy:hover::before {
  left: 100%;
}

.card-sexy:hover {
  transform: translateY(-8px) scale(1.03);
  box-shadow: 
    0 25px 50px rgba(59, 130, 246, 0.15),
    0 15px 35px rgba(147, 51, 234, 0.1),
    0 5px 15px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.card-sexy:hover .card-icon {
  transform: scale(1.15) rotate(5deg);
  filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.5));
}

.card-sexy:hover .card-content {
  transform: translateY(-2px);
}

.card-sexy .card-icon {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center;
}

.card-sexy .card-content {
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

/* Glow Effect for Cards */
.card-glow:hover {
  animation: card-glow 2s ease-in-out;
  border-color: rgba(147, 51, 234, 0.5);
}

/* Pulse Effect for Special Cards */
.card-pulse:hover {
  animation: card-pulse 1s ease-in-out 3;
}

/* Floating Animation for Cards */
.card-float:hover {
  animation: float 2s ease-in-out infinite;
}

/* Floating Animation for Icons */
.icon-float {
  animation: float 2s ease-in-out infinite;
}

.icon-float:nth-child(2) {
  animation-delay: 0.5s;
}

.icon-float:nth-child(3) {
  animation-delay: 1s;
}

.icon-float:nth-child(4) {
  animation-delay: 1.5s;
}
# Gunicorn configuration file for ChainVerdict Backend
# Optimized for PDF processing and AI workloads

import multiprocessing
import os

# Server socket
bind = "0.0.0.0:3000"
backlog = 2048

# Worker processes
workers = min(4, (multiprocessing.cpu_count() * 2) + 1)
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 50

# Timeout settings - Increased for PDF processing
timeout = 300  # 5 minutes for worker timeout
keepalive = 2
graceful_timeout = 30

# Memory management
max_worker_memory = 1024 * 1024 * 1024  # 1GB per worker
preload_app = False  # Don't preload to save memory

# Logging
accesslog = "-"
errorlog = "-"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = "chainverdict-backend"

# Security
limit_request_line = 8192
limit_request_fields = 100
limit_request_field_size = 8192

# Performance tuning
enable_stdio_inheritance = True
reuse_port = True

# Environment-specific settings
if os.getenv("RENDER"):
    # Render.com specific optimizations
    workers = 2  # Reduce workers on Render to save memory
    timeout = 600  # 10 minutes on Render for complex PDF processing
    max_worker_memory = 512 * 1024 * 1024  # 512MB per worker on Render
    
    # Render uses PORT environment variable
    port = os.getenv("PORT", "3000")
    bind = f"0.0.0.0:{port}"

def when_ready(server):
    """Called just after the server is started."""
    server.log.info("ChainVerdict Backend server is ready. Timeout: %s seconds", timeout)

def worker_int(worker):
    """Called just after a worker has been killed by a signal."""
    worker.log.info("Worker %s killed by signal", worker.pid)

def pre_fork(server, worker):
    """Called just before a worker is forked."""
    server.log.info("Worker %s about to be forked", worker.pid)

def post_fork(server, worker):
    """Called just after a worker has been forked."""
    server.log.info("Worker %s forked", worker.pid)
